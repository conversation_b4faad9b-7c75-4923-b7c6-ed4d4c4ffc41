import { <PERSON>ceManager } from './DeviceManager';
import { ExternalDeviceManager, DeviceResolutionOptions } from './ExternalDeviceManager';
import { BaseResolveDeviceProps } from './PlatformManager';
import { platformRegistry } from '../../core/PlatformRegistry';
import { CommandError } from '../../utils/errors';

/**
 * Factory class for creating device managers across all platforms.
 * 
 * This factory provides a unified interface for device manager creation,
 * handling both built-in platforms (iOS, Android) and external platforms.
 * It abstracts away the differences between platform-specific device
 * management implementations.
 */
export class DeviceManagerFactory {
  /**
   * Create a device manager for the specified platform.
   * 
   * This method handles the complexity of different device manager patterns:
   * - Built-in platforms use their own specific device managers
   * - External platforms use standardized ExternalDeviceManager implementations
   * - Fallback to legacy resolver functions for backward compatibility
   * 
   * @param platform Platform identifier (e.g., 'ios', 'android', 'windows')
   * @param options Device resolution options
   * @returns Promise resolving to a device manager instance
   */
  static async createForPlatform(
    platform: string,
    options?: BaseResolveDeviceProps<any>
  ): Promise<DeviceManager<any>> {
    // Handle built-in platforms
    if (platform === 'ios') {
      const { AppleDeviceManager } = await import('./ios/AppleDeviceManager');
      return AppleDeviceManager.resolveAsync(options);
    }
    
    if (platform === 'android') {
      const { AndroidDeviceManager } = await import('./android/AndroidDeviceManager');
      return AndroidDeviceManager.resolveAsync(options);
    }

    // Handle external platforms
    const platformData = platformRegistry.getPlatform(platform);
    if (!platformData) {
      throw new CommandError(
        `Platform "${platform}" not found. Available platforms: ${platformRegistry
          .getAvailablePlatforms()
          .join(', ')}`
      );
    }

    // Try new standardized device manager class first
    if (platformData.deviceManagerClass) {
      return platformData.deviceManagerClass.resolveAsync(options);
    }

    // Fallback to legacy resolver function for backward compatibility
    if (platformData.resolveDeviceAsync) {
      return platformData.resolveDeviceAsync(options);
    }

    throw new CommandError(
      `Platform "${platform}" does not provide device management. ` +
        `The platform must implement either 'deviceManagerClass' or 'resolveDeviceAsync'.`
    );
  }

  /**
   * Create a device manager with enhanced options.
   * 
   * This method provides additional device resolution capabilities
   * including device type filtering and platform-specific options.
   * 
   * @param platform Platform identifier
   * @param options Enhanced device resolution options
   * @returns Promise resolving to a device manager instance
   */
  static async createWithOptions<TDevice = any>(
    platform: string,
    options?: DeviceResolutionOptions<TDevice>
  ): Promise<DeviceManager<TDevice>> {
    // Convert enhanced options to base options format
    const baseOptions: BaseResolveDeviceProps<TDevice> = {
      device: options?.device as TDevice,
      shouldPrompt: options?.shouldPrompt,
    };

    const manager = await this.createForPlatform(platform, baseOptions);

    // Apply additional filtering if device type is specified
    if (options?.deviceType && manager instanceof ExternalDeviceManager) {
      const actualDeviceType = manager.getDeviceType();
      if (actualDeviceType !== options.deviceType) {
        throw new CommandError(
          `Device type mismatch: requested "${options.deviceType}" but got "${actualDeviceType}"`
        );
      }
    }

    return manager;
  }

  /**
   * Get available device managers for a platform.
   * 
   * This method discovers all available devices for a platform
   * and returns device managers for each one.
   * 
   * @param platform Platform identifier
   * @returns Promise resolving to array of device managers
   */
  static async getAvailableDeviceManagers(
    platform: string
  ): Promise<DeviceManager<any>[]> {
    const platformData = platformRegistry.getPlatform(platform);
    if (!platformData) {
      return [];
    }

    // For external platforms with standardized device managers
    if (platformData.deviceManagerClass) {
      try {
        // Create a temporary instance to get available devices
        const tempManager = await platformData.deviceManagerClass.resolveAsync();
        if (tempManager instanceof ExternalDeviceManager) {
          const devices = await tempManager.getDevicesAsync();
          return Promise.all(
            devices.map((device) => 
              platformData.deviceManagerClass!.resolveAsync({ device })
            )
          );
        }
      } catch (error) {
        // If device discovery fails, return empty array
        console.warn(`Failed to discover devices for platform ${platform}:`, error);
      }
    }

    return [];
  }

  /**
   * Check if a platform supports device management.
   * 
   * @param platform Platform identifier
   * @returns True if the platform provides device management
   */
  static supportsPlatform(platform: string): boolean {
    // Built-in platforms always support device management
    if (platform === 'ios' || platform === 'android') {
      return true;
    }

    // Check external platforms
    const platformData = platformRegistry.getPlatform(platform);
    return !!(platformData?.deviceManagerClass || platformData?.resolveDeviceAsync);
  }

  /**
   * Get the device management capabilities for a platform.
   * 
   * @param platform Platform identifier
   * @returns Object describing device management capabilities
   */
  static getPlatformCapabilities(platform: string): {
    hasDeviceManagement: boolean;
    usesStandardizedInterface: boolean;
    supportsDeviceDiscovery: boolean;
  } {
    if (platform === 'ios' || platform === 'android') {
      return {
        hasDeviceManagement: true,
        usesStandardizedInterface: true,
        supportsDeviceDiscovery: true,
      };
    }

    const platformData = platformRegistry.getPlatform(platform);
    return {
      hasDeviceManagement: !!(platformData?.deviceManagerClass || platformData?.resolveDeviceAsync),
      usesStandardizedInterface: !!platformData?.deviceManagerClass,
      supportsDeviceDiscovery: !!platformData?.deviceManagerClass,
    };
  }
}
