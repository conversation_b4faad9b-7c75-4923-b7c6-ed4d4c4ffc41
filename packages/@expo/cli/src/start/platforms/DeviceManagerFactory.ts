import { Device<PERSON>anager } from './DeviceManager';
import { BaseResolveDeviceProps } from './PlatformManager';
import { platformRegistry } from '../../core/PlatformRegistry';
import { CommandError } from '../../utils/errors';

/**
 * Helper interface for device resolution options.
 * This provides a standardized way to pass device selection criteria.
 */
export interface DeviceResolutionOptions<TDevice = any> {
  /** Specific device to target */
  device?: Partial<TDevice>;
  /** Whether to prompt user for device selection */
  shouldPrompt?: boolean;
  /** Additional platform-specific options */
  [key: string]: any;
}

/**
 * Factory class for creating device managers across all platforms.
 *
 * This factory provides a unified interface for device manager creation,
 * handling both built-in platforms (iOS, Android) and external platforms.
 * It abstracts away the differences between platform-specific device
 * management implementations.
 */
export class DeviceManagerFactory {
  /**
   * Create a device manager for the specified platform.
   *
   * This method handles the complexity of different device manager patterns:
   * - Built-in platforms use their own specific device managers
   * - External platforms use standardized DeviceManager implementations
   * - Fallback to legacy resolver functions for backward compatibility
   *
   * @param platform Platform identifier (e.g., 'ios', 'android', 'windows')
   * @param options Device resolution options
   * @returns Promise resolving to a device manager instance
   */
  static async createForPlatform(
    platform: string,
    options?: BaseResolveDeviceProps<any>
  ): Promise<DeviceManager<any>> {
    // Handle built-in platforms
    if (platform === 'ios') {
      const { AppleDeviceManager } = await import('./ios/AppleDeviceManager');
      return AppleDeviceManager.resolveAsync(options);
    }

    if (platform === 'android') {
      const { AndroidDeviceManager } = await import('./android/AndroidDeviceManager');
      return AndroidDeviceManager.resolveAsync(options);
    }

    // Handle external platforms
    const platformData = platformRegistry.getPlatform(platform);
    if (!platformData) {
      throw new CommandError(
        `Platform "${platform}" not found. Available platforms: ${platformRegistry
          .getAvailablePlatforms()
          .join(', ')}`
      );
    }

    // Try standardized device manager constructor first
    if (platformData.platformManagerConstructor) {
      // External platforms should provide a static resolveAsync method
      const DeviceManagerClass = platformData.platformManagerConstructor as any;
      if (DeviceManagerClass.resolveAsync) {
        return DeviceManagerClass.resolveAsync(options);
      }
    }

    // Fallback to legacy resolver function for backward compatibility
    if (platformData.resolveDeviceAsync) {
      return platformData.resolveDeviceAsync(options);
    }

    throw new CommandError(
      `Platform "${platform}" does not provide device management. ` +
        `The platform must implement either a DeviceManager class with resolveAsync or 'resolveDeviceAsync'.`
    );
  }

  /**
   * Create a device manager with enhanced options.
   *
   * This method provides additional device resolution capabilities
   * with platform-specific options.
   *
   * @param platform Platform identifier
   * @param options Enhanced device resolution options
   * @returns Promise resolving to a device manager instance
   */
  static async createWithOptions<TDevice = any>(
    platform: string,
    options?: DeviceResolutionOptions<TDevice>
  ): Promise<DeviceManager<TDevice>> {
    // Convert enhanced options to base options format
    const baseOptions: BaseResolveDeviceProps<TDevice> = {
      device: options?.device as TDevice,
      shouldPrompt: options?.shouldPrompt,
    };

    return this.createForPlatform(platform, baseOptions);
  }

  /**
   * Check if a platform supports device management.
   *
   * @param platform Platform identifier
   * @returns True if the platform provides device management
   */
  static supportsPlatform(platform: string): boolean {
    // Built-in platforms always support device management
    if (platform === 'ios' || platform === 'android') {
      return true;
    }

    // Check external platforms
    const platformData = platformRegistry.getPlatform(platform);
    return !!(platformData?.platformManagerConstructor || platformData?.resolveDeviceAsync);
  }

  /**
   * Get the device management capabilities for a platform.
   *
   * @param platform Platform identifier
   * @returns Object describing device management capabilities
   */
  static getPlatformCapabilities(platform: string): {
    hasDeviceManagement: boolean;
    usesStandardizedInterface: boolean;
  } {
    if (platform === 'ios' || platform === 'android') {
      return {
        hasDeviceManagement: true,
        usesStandardizedInterface: true,
      };
    }

    const platformData = platformRegistry.getPlatform(platform);
    return {
      hasDeviceManagement: !!(platformData?.platformManagerConstructor || platformData?.resolveDeviceAsync),
      usesStandardizedInterface: !!platformData?.platformManagerConstructor,
    };
  }
}
