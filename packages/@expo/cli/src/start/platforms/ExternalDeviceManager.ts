import { <PERSON><PERSON><PERSON>ana<PERSON> } from './DeviceManager';
import { BaseResolveDeviceProps } from './PlatformManager';

/**
 * Standardized base class for external platform device managers.
 * 
 * This class provides a consistent interface that external platforms must implement
 * to integrate with Expo CLI's device management system. It extends the base
 * DeviceManager class and adds external platform-specific requirements.
 * 
 * External platforms should extend this class and implement all abstract methods
 * to provide proper device management integration.
 */
export abstract class ExternalDeviceManager<TDevice> extends DeviceManager<TDevice> {
  /**
   * Static method to resolve and create a device manager instance.
   * This is the primary entry point for device resolution.
   * 
   * @param options Device resolution options including device selection and prompting
   * @returns Promise resolving to a device manager instance
   */
  abstract static resolveAsync(
    options?: BaseResolveDeviceProps<Partial<TDevice>>
  ): Promise<ExternalDeviceManager<TDevice>>;

  /**
   * Get the type of device this manager represents.
   * This helps the CLI understand how to interact with the device.
   * 
   * @returns The device type category
   */
  abstract getDeviceType(): 'simulator' | 'emulator' | 'physical';

  /**
   * Validate that the device is available and ready for use.
   * This should check device connectivity, availability, and any prerequisites.
   * 
   * @returns Promise resolving to true if device is valid and ready
   */
  abstract validateDevice(): Promise<boolean>;

  /**
   * Launch a simulator/emulator if this device represents one.
   * This method is optional and should only be implemented for virtual devices.
   * Physical devices should throw an error or return immediately.
   * 
   * @returns Promise that resolves when the simulator/emulator is launched
   */
  abstract launchSimulator?(): Promise<void>;

  /**
   * Install an application on the device from a binary path.
   * This should handle platform-specific installation procedures.
   * 
   * @param appPath Path to the application binary to install
   * @returns Promise that resolves when installation is complete
   */
  abstract installApp(appPath: string): Promise<void>;

  /**
   * Launch an installed application on the device.
   * This should start the application and bring it to the foreground.
   * 
   * @param appId Platform-specific application identifier
   * @returns Promise that resolves when the app is launched
   */
  abstract launchApp(appId: string): Promise<void>;

  /**
   * Get a list of available devices for this platform.
   * This is used for device discovery and selection.
   * 
   * @returns Promise resolving to array of available devices
   */
  abstract getDevicesAsync(): Promise<TDevice[]>;

  /**
   * Check if a specific application is installed on the device.
   * This helps determine if installation is needed before launching.
   * 
   * @param appId Platform-specific application identifier
   * @returns Promise resolving to true if the app is installed
   */
  abstract isAppInstalled(appId: string): Promise<boolean>;

  /**
   * Get platform-specific device information.
   * This provides additional metadata about the device that may be useful
   * for debugging or display purposes.
   * 
   * @returns Object containing platform-specific device information
   */
  abstract getDeviceInfo(): Promise<{ [key: string]: any }>;

  // Inherited from DeviceManager base class:
  // - abstract get name(): string;
  // - abstract get identifier(): string;
  // - abstract startAsync(): Promise<TDevice>;
  // - abstract getAppVersionAsync(applicationId: string, options?: { containerPath?: string }): Promise<string | null>;
  // - abstract installAppAsync(binaryPath: string): Promise<void>;
  // - abstract uninstallAppAsync(applicationId: string): Promise<void>;
  // - abstract isAppInstalledAndIfSoReturnContainerPathForIOSAsync(applicationId: string): Promise<boolean | string>;
  // - abstract openUrlAsync(url: string, options?: { appId?: string }): Promise<void>;
  // - abstract activateWindowAsync(): Promise<void>;
  // - abstract ensureExpoGoAsync(sdkVersion: string): Promise<boolean>;
  // - abstract getExpoGoAppId(): string;
}

/**
 * Type definition for external platform device manager constructor.
 * This ensures that external platform device managers can be instantiated
 * with the correct static methods.
 */
export interface ExternalDeviceManagerConstructor<TDevice> {
  new (device: TDevice): ExternalDeviceManager<TDevice>;
  resolveAsync(
    options?: BaseResolveDeviceProps<Partial<TDevice>>
  ): Promise<ExternalDeviceManager<TDevice>>;
}

/**
 * Helper type for device manager resolution options.
 * This provides a standardized way to pass device selection criteria.
 */
export interface DeviceResolutionOptions<TDevice = any> {
  /** Specific device to target */
  device?: Partial<TDevice>;
  /** Whether to prompt user for device selection */
  shouldPrompt?: boolean;
  /** Platform-specific device type filter */
  deviceType?: 'simulator' | 'emulator' | 'physical';
  /** Additional platform-specific options */
  [key: string]: any;
}

/**
 * Standard device interface that external platforms should implement.
 * This provides a minimal set of properties that all devices should have.
 */
export interface StandardDevice {
  /** Unique device identifier */
  id: string;
  /** Human-readable device name */
  name: string;
  /** Whether the device is available for use */
  isAvailable: boolean;
  /** Device type category */
  type?: 'simulator' | 'emulator' | 'physical' | string;
}
