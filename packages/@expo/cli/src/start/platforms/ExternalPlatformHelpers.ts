import { DeviceManager } from './DeviceManager';
import { BaseResolveDeviceProps } from './PlatformManager';
import { CommandError } from '../../utils/errors';

/**
 * Helper utilities for external platforms to implement device management.
 * 
 * These utilities provide common patterns and implementations that external
 * platforms can use to integrate with Expo CLI's device management system
 * without having to reimplement common functionality.
 */

/**
 * Standard device interface that external platforms should implement.
 * This provides a minimal set of properties that all devices should have.
 */
export interface StandardDevice {
  /** Unique device identifier */
  id: string;
  /** Human-readable device name */
  name: string;
  /** Whether the device is available for use */
  isAvailable: boolean;
  /** Device type category */
  type?: 'simulator' | 'emulator' | 'physical' | string;
  /** Additional platform-specific properties */
  [key: string]: any;
}

/**
 * Helper function to create a device resolver for external platforms.
 * This provides a standardized pattern for device resolution that handles
 * common scenarios like device selection and prompting.
 * 
 * @param getDevices Function to get available devices
 * @param createDeviceManager Function to create a device manager for a device
 * @param promptForDevice Optional function to prompt user for device selection
 * @returns Device resolver function that can be used in ExternalPlatform
 */
export function createDeviceResolver<TDevice extends StandardDevice>(
  getDevices: () => Promise<TDevice[]>,
  createDeviceManager: (device: TDevice) => DeviceManager<TDevice>,
  promptForDevice?: (devices: TDevice[]) => Promise<TDevice>
) {
  return async (options?: BaseResolveDeviceProps<TDevice>): Promise<DeviceManager<TDevice>> => {
    const devices = await getDevices();
    
    if (devices.length === 0) {
      throw new CommandError('No devices available for this platform');
    }

    let selectedDevice: TDevice;

    if (options?.device) {
      // Try to find the specified device
      selectedDevice = findDeviceByIdOrName(devices, options.device);
    } else if (options?.shouldPrompt && promptForDevice) {
      // Prompt user to select a device
      selectedDevice = await promptForDevice(devices);
    } else {
      // Use the first available device
      selectedDevice = devices.find(d => d.isAvailable) || devices[0];
    }

    return createDeviceManager(selectedDevice);
  };
}

/**
 * Helper function to find a device by ID or name.
 * This provides consistent device matching logic across platforms.
 * 
 * @param devices Array of available devices
 * @param deviceIdOrName Device identifier or name to search for
 * @returns The matching device
 * @throws CommandError if device is not found
 */
export function findDeviceByIdOrName<TDevice extends StandardDevice>(
  devices: TDevice[],
  deviceIdOrName: Partial<TDevice> | string
): TDevice {
  if (typeof deviceIdOrName === 'string') {
    // Search by ID first, then by name
    const device = devices.find(d => 
      d.id === deviceIdOrName || 
      d.name.toLowerCase().includes(deviceIdOrName.toLowerCase())
    );
    
    if (!device) {
      throw new CommandError(
        `Device "${deviceIdOrName}" not found. Available devices: ${devices
          .map(d => `${d.name} (${d.id})`)
          .join(', ')}`
      );
    }
    
    return device;
  } else {
    // Search by device properties
    const device = devices.find(d => {
      return Object.entries(deviceIdOrName).every(([key, value]) => 
        d[key] === value
      );
    });
    
    if (!device) {
      throw new CommandError(
        `Device matching criteria not found. Available devices: ${devices
          .map(d => `${d.name} (${d.id})`)
          .join(', ')}`
      );
    }
    
    return device;
  }
}

/**
 * Helper function to filter devices by availability.
 * This provides consistent device filtering logic across platforms.
 * 
 * @param devices Array of devices to filter
 * @param includeUnavailable Whether to include unavailable devices
 * @returns Filtered array of devices
 */
export function filterAvailableDevices<TDevice extends StandardDevice>(
  devices: TDevice[],
  includeUnavailable = false
): TDevice[] {
  return includeUnavailable ? devices : devices.filter(d => d.isAvailable);
}

/**
 * Helper function to sort devices by preference.
 * This provides consistent device ordering logic across platforms.
 * Physical devices are typically preferred over virtual ones.
 * 
 * @param devices Array of devices to sort
 * @returns Sorted array of devices
 */
export function sortDevicesByPreference<TDevice extends StandardDevice>(
  devices: TDevice[]
): TDevice[] {
  return devices.sort((a, b) => {
    // Physical devices first
    if (a.type === 'physical' && b.type !== 'physical') return -1;
    if (b.type === 'physical' && a.type !== 'physical') return 1;
    
    // Then available devices
    if (a.isAvailable && !b.isAvailable) return -1;
    if (b.isAvailable && !a.isAvailable) return 1;
    
    // Finally by name
    return a.name.localeCompare(b.name);
  });
}

/**
 * Helper function to validate device manager implementation.
 * This can be used by external platforms to ensure their device managers
 * implement all required methods correctly.
 * 
 * @param deviceManager Device manager instance to validate
 * @returns Array of validation errors (empty if valid)
 */
export function validateDeviceManager(deviceManager: DeviceManager<any>): string[] {
  const errors: string[] = [];
  
  // Check required properties
  if (!deviceManager.name) {
    errors.push('Device manager must have a name property');
  }
  
  if (!deviceManager.identifier) {
    errors.push('Device manager must have an identifier property');
  }
  
  // Check required methods
  const requiredMethods = [
    'startAsync',
    'getAppVersionAsync',
    'installAppAsync',
    'uninstallAppAsync',
    'isAppInstalledAndIfSoReturnContainerPathForIOSAsync',
    'openUrlAsync',
    'activateWindowAsync',
    'ensureExpoGoAsync',
    'getExpoGoAppId'
  ];
  
  for (const method of requiredMethods) {
    if (typeof (deviceManager as any)[method] !== 'function') {
      errors.push(`Device manager must implement ${method} method`);
    }
  }
  
  return errors;
}

/**
 * Re-export commonly used types and classes for external platforms.
 * This provides a single import point for external platform development.
 */
export { DeviceManager, BaseResolveDeviceProps };
export type { ExternalPlatformDeviceResolver } from '../core/PlatformRegistry';
